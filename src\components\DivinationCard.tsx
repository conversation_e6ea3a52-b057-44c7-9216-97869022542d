import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useThemeStore } from '@/stores/themeStore';
import { Heart, Star, Sparkles } from 'lucide-react';

interface DivinationCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  glowing?: boolean;
  delay?: number; // 滚动动画延迟
}

export const DivinationCard: React.FC<DivinationCardProps> = ({
  title,
  children,
  className,
  glowing = false,
  delay = 0
}) => {
  const { currentTheme } = useThemeStore();
  const cardRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  // 滚动动画观察器
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delay * 100);
        }
      },
      { threshold: 0.1 }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, [delay]);

  const getCuteDecorations = () => {
    if (currentTheme !== 'cute') return null;
    
    return (
      <div className="absolute -top-1 -right-1 flex space-x-1">
        <Star className="w-3 h-3 text-accent animate-pulse" />
        <Heart className="w-3 h-3 text-primary animate-bounce" />
      </div>
    );
  };

  if (currentTheme === 'cute') {
    return (
      <Card
        ref={cardRef}
        className={cn(
          "cute-card relative overflow-hidden transition-all duration-300",
          "bg-card border-2 border-primary/30 shadow-lg rounded-2xl",
          "divination-card-enhanced",
          glowing && "glow-border",
          isVisible ? "scroll-fade-in" : "opacity-0",
          className
        )}
        style={{ animationDelay: `${delay * 0.1}s` }}
      >
        {getCuteDecorations()}
        <CardHeader className="bg-gradient-to-r from-primary/15 to-secondary/15 rounded-t-2xl pb-3">
          <CardTitle className="text-primary font-bold text-lg flex items-center justify-center gap-2">
            <Sparkles className="w-4 h-4 text-primary" />
            {title}
            <Heart className="w-4 h-4 text-accent" />
          </CardTitle>
        </CardHeader>
        <CardContent className="bg-card/90 text-cardForeground p-4 rounded-b-2xl">
          {children}
        </CardContent>
      </Card>
    );
  }

  // 其他主题的增强样式
  return (
    <Card
      ref={cardRef}
      className={cn(
        "divination-card-enhanced",
        glowing && "glow-border",
        isVisible ? "scroll-fade-in" : "opacity-0",
        className
      )}
      style={{ animationDelay: `${delay * 0.1}s` }}
    >
      <CardHeader className="pb-4">
        <CardTitle className="gradient-text text-xl font-bold">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
};