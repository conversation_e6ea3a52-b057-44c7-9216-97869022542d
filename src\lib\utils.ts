import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Translation mapping for Six Spirits
const SIX_SPIRITS_MAP: Record<string, string> = {
  '青龙': 'qingLong',
  '朱雀': 'zhuQue',
  '勾陈': 'gouChen',
  '螣蛇': 'tengShe',
  '白虎': 'baiHu',
  '玄武': 'xuanWu'
};

// Translation mapping for Six Relations
const SIX_RELATIONS_MAP: Record<string, string> = {
  '官鬼': 'guanGui',
  '妻财': 'qiCai',
  '兄弟': 'xiongDi',
  '父母': 'fuMu',
  '子孙': 'ziSun'
};

// Translation mapping for Heavenly Stems (天干)
const HEAVENLY_STEMS_MAP: Record<string, { zh: string; en: string }> = {
  '甲': { zh: 'stemJia', en: 'stemJiaEn' },
  '乙': { zh: 'stemYi', en: 'stemYiEn' },
  '丙': { zh: 'stemBing', en: 'stemBingEn' },
  '丁': { zh: 'stemDing', en: 'stemDingEn' },
  '戊': { zh: 'stemWu', en: 'stemWuEn' },
  '己': { zh: 'stemJi', en: 'stemJiEn' },
  '庚': { zh: 'stemGeng', en: 'stemGengEn' },
  '辛': { zh: 'stemXin', en: 'stemXinEn' },
  '壬': { zh: 'stemRen', en: 'stemRenEn' },
  '癸': { zh: 'stemGui', en: 'stemGuiEn' }
};

// Translation mapping for Earthly Branches (地支)
const EARTHLY_BRANCHES_MAP: Record<string, { zh: string; en: string }> = {
  '子': { zh: 'branchZi', en: 'branchZiEn' },
  '丑': { zh: 'branchChou', en: 'branchChouEn' },
  '寅': { zh: 'branchYin', en: 'branchYinEn' },
  '卯': { zh: 'branchMao', en: 'branchMaoEn' },
  '辰': { zh: 'branchChen', en: 'branchChenEn' },
  '巳': { zh: 'branchSi', en: 'branchSiEn' },
  '午': { zh: 'branchWu', en: 'branchWuEn' },
  '未': { zh: 'branchWei', en: 'branchWeiEn' },
  '申': { zh: 'branchShen', en: 'branchShenEn' },
  '酉': { zh: 'branchYou', en: 'branchYouEn' },
  '戌': { zh: 'branchXu', en: 'branchXuEn' },
  '亥': { zh: 'branchHai', en: 'branchHaiEn' }
};

// Translation mapping for Five Elements (五行)
const FIVE_ELEMENTS_MAP: Record<string, { zh: string; en: string }> = {
  '金': { zh: 'elementJin', en: 'elementJinEn' },
  '木': { zh: 'elementMu', en: 'elementMuEn' },
  '水': { zh: 'elementShui', en: 'elementShuiEn' },
  '火': { zh: 'elementHuo', en: 'elementHuoEn' },
  '土': { zh: 'elementTu', en: 'elementTuEn' }
};

// Function to translate Six Spirits
export function translateSixSpirit(chineseTerm: string, t: (key: string) => string): string {
  const translationKey = SIX_SPIRITS_MAP[chineseTerm];
  if (translationKey) {
    return t(translationKey);
  }
  return chineseTerm; // Return original if no translation found
}

// Function to translate Six Relations
export function translateSixRelation(chineseTerm: string, t: (key: string) => string, currentLanguage?: string): string {
  // Extract the relation part (first two characters)
  const relationPart = chineseTerm.substring(0, 2);
  const translationKey = SIX_RELATIONS_MAP[relationPart];

  if (translationKey) {
    // Get the translated relation
    const translatedRelation = t(translationKey);

    // If the term has additional parts (like 戌土), translate them too
    if (chineseTerm.length > 2) {
      const remainingPart = chineseTerm.substring(2);
      const translatedRemaining = translateStemBranchElement(remainingPart, t, currentLanguage);
      // Add space between relation and stem/branch in English
      return translatedRelation + ' ' + translatedRemaining;
    }

    return translatedRelation;
  }

  return chineseTerm; // Return original if no translation found
}

// Function to translate Heavenly Stems, Earthly Branches, and Five Elements
export function translateStemBranchElement(chineseTerm: string, t: (key: string) => string, currentLanguage?: string): string {
  let result = '';
  let i = 0;
  
  while (i < chineseTerm.length) {
    let translated = false;
    
    // Try to match Heavenly Stem (天干)
    if (i < chineseTerm.length) {
      const stem = chineseTerm[i];
      const stemKey = HEAVENLY_STEMS_MAP[stem];
      if (stemKey) {
        result += t(stemKey);
        i++;
        translated = true;
      }
    }
    
    // Try to match Earthly Branch (地支)
    if (!translated && i < chineseTerm.length) {
      const branch = chineseTerm[i];
      const branchKey = EARTHLY_BRANCHES_MAP[branch];
      if (branchKey) {
        result += t(branchKey);
        i++;
        translated = true;
      }
    }
    
    // Try to match Five Element (五行)
    if (!translated && i < chineseTerm.length) {
      const element = chineseTerm[i];
      const elementKey = FIVE_ELEMENTS_MAP[element];
      if (elementKey) {
        result += t(elementKey);
        i++;
        translated = true;
      }
    }
    
    // If no translation found, keep the original character
    if (!translated) {
      result += chineseTerm[i];
      i++;
    }
  }
  
  // Add spaces between stem/branch and element only in English
  // This will convert "XuTu" to "Xu Tu", "YinMu" to "Yin Mu", etc.
  if (currentLanguage === 'en') {
    return result.replace(/([A-Z][a-z]+)([A-Z][a-z]+)/g, '$1 $2');
  }

  return result;
}
