# 六爻排盘网站 UI/UX 设计系统重构文档
# FortuneTeller.Today UI/UX Design System Redesign

## 📋 项目概述

### 当前网站结构分析
- **技术栈**: React + TypeScript + Tailwind CSS
- **主题系统**: 三种风格主题 (modern/cute/classic)
- **核心功能**: 六爻占卜、AI分析、用户认证、主题切换
- **响应式**: 基础移动端适配，需要优化

### 🚫 设计约束原则
- **绝不修改业务逻辑**: 保持所有JavaScript交互行为不变
- **仅限UI层面**: 布局、样式、视觉、交互动画优化
- **保持功能完整**: 导航、表单提交、Modal等核心功能不变

---

## 🎨 设计系统定义

### 颜色系统
基于现有CSS变量系统，优化三主题配色：

#### Modern Theme (科技风)
```css
:root {
  --background: 240 10% 4%;           /* 深色背景 */
  --foreground: 210 40% 95%;          /* 浅色文字 */
  --primary: 230 40% 20%;             /* 主色调 */
  --secondary: 45 90% 55%;            /* 金色强调 */
  --accent: 270 50% 25%;              /* 紫色点缀 */
}
```

#### Cute Theme (暖色风)
```css
:root {
  --primary: 340 75% 55%;             /* 粉色主调 */
  --secondary: 45 95% 60%;            /* 暖黄色 */
  --accent: 280 60% 65%;              /* 淡紫色 */
}
```

#### Classic Theme (国学风)
```css
:root {
  --primary: 25 75% 45%;              /* 古铜色 */
  --secondary: 45 85% 50%;            /* 金黄色 */
  --accent: 15 60% 35%;               /* 深棕色 */
}
```

### 字体系统
```css
/* 主字体栈 */
.font-primary {
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 中文优化字体 */
.font-chinese {
  font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 经典主题衬线字体 */
.font-classic {
  font-family: 'Playfair Display', 'Times New Roman', serif;
}
```

### Spacing Scale (基于4px)
```css
.spacing-system {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
}
```

---

## 🏗️ Layout 重构方案

### Container 系统
```css
/* 统一容器宽度 */
.main-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* 内容区域容器 */
.content-container {
  @apply max-w-4xl mx-auto;
}

/* 宽内容容器 */
.wide-container {
  @apply max-w-6xl mx-auto;
}
```

### 响应式断点策略
- **Mobile**: < 640px (单列布局，紧凑间距)
- **Tablet**: 640px - 1024px (2列布局，中等间距)
- **Desktop**: > 1024px (3列布局，宽松间距)

### Header 布局重构
```tsx
// 优化后的Header布局
<header className="sticky top-0 z-50 border-b border-border/50 bg-card/80 backdrop-blur-md">
  <div className="main-container">
    <div className="grid grid-cols-[1fr_auto] lg:grid-cols-[auto_1fr_auto] gap-4 py-4 items-center">
      {/* Logo区域 */}
      <div className="flex items-center space-x-3">
        <h1 className="text-2xl font-bold gradient-text cursor-pointer">
          {t('appName')}
        </h1>
        <Badge variant="secondary" className="text-xs hidden sm:inline-flex">
          {t('aiAnalysis')}
        </Badge>
      </div>
      
      {/* 桌面端导航 */}
      <nav className="hidden lg:flex items-center justify-center space-x-8">
        <Button variant="ghost" className="text-sm font-medium">
          {t('features')}
        </Button>
        <Button variant="ghost" className="text-sm font-medium">
          {t('pricing')}
        </Button>
      </nav>
      
      {/* 右侧工具栏 */}
      <div className="flex items-center space-x-3">
        <LanguageSwitcher />
        <ThemeSwitcher />
        {/* 移动端菜单按钮 */}
        <Button variant="ghost" size="sm" className="lg:hidden">
          <Menu className="h-5 w-5" />
        </Button>
        {/* 用户菜单保持不变 */}
      </div>
    </div>
  </div>
</header>
```

### Hero 区域重构
```tsx
// 优化后的Hero布局 - Modern主题
<section className="relative min-h-screen flex items-center justify-center overflow-hidden">
  {/* 背景层 */}
  <div 
    className="absolute inset-0 bg-cover bg-center bg-fixed"
    style={{
      backgroundImage: `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url(${modernBg})`,
    }}
  />
  
  {/* 内容层 */}
  <div className="relative z-10 main-container text-center">
    <div className="content-container space-y-8 sm:space-y-12">
      {/* 标题区域 */}
      <div className="space-y-6">
        <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold gradient-text leading-tight">
          {getThemeTitle()}
        </h1>
        <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
          {getThemeDescription()}
        </p>
      </div>
      
      {/* CTA按钮区域 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <Button 
          variant="golden" 
          size="lg" 
          className="text-lg px-8 sm:px-12 py-4 sm:py-6 rounded-xl hover:scale-105 transition-transform duration-300"
        >
          <Zap className="w-5 h-5 mr-2" />
          {t('startAiAnalysis')}
        </Button>
        <Button 
          variant="outline" 
          size="lg" 
          className="text-lg px-8 py-4 rounded-xl hover:bg-card/50 transition-colors duration-300"
        >
          {t('learnMore')}
        </Button>
      </div>
    </div>
  </div>
  
  {/* 滚动指示器 */}
  <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <ChevronDown className="w-6 h-6 text-muted-foreground" />
  </div>
</section>
```

### Features 卡片区域重构
```tsx
// 优化后的Features布局
<section className="section-spacing">
  <div className="main-container">
    {/* 标题区域 */}
    <div className="text-center content-spacing mb-12 lg:mb-16">
      <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold gradient-text mb-6">
        {t('coreFeaturesHighlight')}
      </h2>
      <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
        {t('professionalAnalysisDesc')}
      </p>
    </div>
    
    {/* 卡片网格 */}
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 auto-rows-fr">
      {features.map((feature, index) => (
        <DivinationCard 
          key={index}
          title={feature.title}
          className="hover:scale-[1.02] transition-transform duration-300"
        >
          {feature.content}
        </DivinationCard>
      ))}
    </div>
  </div>
</section>
```

---

---

## 🎭 交互微动效定义

### 动画时长系统
```css
:root {
  --transition-fast: 150ms ease-out;
  --transition-normal: 300ms ease-out;
  --transition-slow: 500ms ease-out;
  --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
```

### 按钮交互效果

#### CTA主按钮 (mystical-button)
```css
.mystical-button {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
  background: var(--gradient-primary);
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.mystical-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 20px var(--primary);
}

.mystical-button:active {
  transform: translateY(0) scale(0.98);
  transition: var(--transition-fast);
}

.mystical-button:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}
```

#### 次要按钮
```css
.secondary-button {
  @apply border border-border bg-card hover:bg-muted transition-all duration-300;
}

.secondary-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
```

### 卡片交互效果

#### DivinationCard 增强
```css
.divination-card {
  @apply rounded-lg border border-border bg-card shadow-lg;
  transition: var(--transition-normal);
  transform: translateY(0) scale(1);
}

.divination-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15), 0 0 0 1px var(--border);
}

/* Cute主题特殊效果 */
.theme-cute .divination-card:hover {
  transform: translateY(-2px) scale(1.03) rotate(0.5deg);
  box-shadow: 0 8px 32px var(--primary);
}
```

### 表单输入框交互
```css
.form-input {
  @apply w-full px-4 py-3 rounded-lg border border-input bg-background;
  transition: var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 3px rgba(var(--ring), 0.1);
  transform: scale(1.01);
}

.form-input:focus-visible {
  ring: 2px solid var(--ring);
  ring-offset: 2px;
}
```

### 滚动动画效果

#### Fade-in on Scroll
```css
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-fade-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* 延迟动画 */
.scroll-fade-in-delay-1 { animation-delay: 0.1s; }
.scroll-fade-in-delay-2 { animation-delay: 0.2s; }
.scroll-fade-in-delay-3 { animation-delay: 0.3s; }
```

#### 视差滚动效果
```css
.parallax-bg {
  transform: translateZ(0);
  will-change: transform;
}

/* JavaScript控制 */
.parallax-bg[data-scroll] {
  transform: translate3d(0, calc(var(--scroll-y) * 0.5px), 0);
}
```

### Loading 状态动画
```css
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 5px var(--primary);
  }
  50% {
    opacity: 0.7;
    box-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary);
  }
}

.loading-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}
```

### Toast 通知动画
```css
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.toast-enter {
  animation: slideInRight 0.3s ease-out;
}
```

### 主题切换动画
```css
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 切换时的淡入效果 */
@keyframes themeChange {
  0% { opacity: 0.8; }
  50% { opacity: 0.9; }
  100% { opacity: 1; }
}

.theme-changing {
  animation: themeChange 0.5s ease-out;
}
```

---

---

## 🎨 素材资源建议

### 背景图片资源

#### Modern Theme (科技风)
```typescript
// 推荐高质量背景图
const modernBackgrounds = {
  hero: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&q=80', // 科技网格
  secondary: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1920&q=80', // 星空科技
  pattern: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=1920&q=80'  // 抽象几何
};

// CSS渐变叠加
const modernGradients = {
  primary: 'linear-gradient(135deg, rgba(15,23,42,0.9) 0%, rgba(30,41,59,0.8) 100%)',
  hero: 'linear-gradient(rgba(0,0,0,0.7), rgba(15,23,42,0.6))',
  card: 'linear-gradient(145deg, rgba(30,41,59,0.95), rgba(15,23,42,0.9))'
};
```

#### Cute Theme (暖色风)
```typescript
const cuteBackgrounds = {
  hero: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1920&q=80', // 粉色云朵
  secondary: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80', // 温暖渐变
  pattern: 'https://images.unsplash.com/photo-1557682250-33bd709cbe85?w=1920&q=80'  // 柔和抽象
};

const cuteGradients = {
  primary: 'linear-gradient(135deg, rgba(255,182,193,0.9) 0%, rgba(255,218,185,0.8) 100%)',
  hero: 'linear-gradient(rgba(255,182,193,0.8), rgba(255,218,185,0.7))',
  card: 'linear-gradient(145deg, rgba(255,240,245,0.95), rgba(255,228,225,0.9))'
};
```

#### Classic Theme (国学风)
```typescript
const classicBackgrounds = {
  hero: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80', // 古典纹理
  secondary: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&q=80', // 山水意境
  pattern: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80'  // 传统图案
};

const classicGradients = {
  primary: 'linear-gradient(135deg, rgba(139,69,19,0.9) 0%, rgba(160,82,45,0.8) 100%)',
  hero: 'linear-gradient(rgba(139,69,19,0.8), rgba(160,82,45,0.6))',
  card: 'linear-gradient(145deg, rgba(222,184,135,0.95), rgba(205,133,63,0.9))'
};
```

### 图标系统

#### 推荐图标库
```typescript
// 使用 Lucide React (已集成)
import {
  Zap, TrendingUp, Shield, Users, Star, Heart,
  Sparkles, Sun, Moon, Palette, Menu, X,
  ChevronDown, ChevronRight, ArrowRight,
  CheckCircle, XCircle, AlertCircle, Info
} from 'lucide-react';

// 主题特色图标映射
const themeIcons = {
  modern: {
    primary: Zap,
    secondary: TrendingUp,
    accent: Shield
  },
  cute: {
    primary: Heart,
    secondary: Star,
    accent: Sparkles
  },
  classic: {
    primary: Sun,
    secondary: Scroll,
    accent: Flower
  }
};
```

#### 自定义SVG图标
```tsx
// 六爻专用图标
const HexagramIcon = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6">
    <path d="M3 6h18M3 12h18M3 18h18" stroke="currentColor" strokeWidth="2"/>
    <circle cx="21" cy="6" r="1" fill="currentColor"/>
    <circle cx="21" cy="18" r="1" fill="currentColor"/>
  </svg>
);

const DivinationIcon = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6">
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/>
    <path d="M12 2v20M2 12h20" stroke="currentColor" strokeWidth="1"/>
  </svg>
);
```

### 插图资源

#### unDraw 插图风格
```typescript
// 推荐 unDraw 插图 (可自定义颜色)
const illustrations = {
  hero: 'https://undraw.co/api/illustrations/fortune_teller',
  features: 'https://undraw.co/api/illustrations/crystal_ball',
  empty_state: 'https://undraw.co/api/illustrations/void',
  success: 'https://undraw.co/api/illustrations/celebration'
};

// 自定义颜色匹配主题
const illustrationColors = {
  modern: '#1e293b',
  cute: '#ec4899',
  classic: '#92400e'
};
```

### 字体资源

#### Google Fonts 集成
```html
<!-- 在 index.html 中添加 -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
```

```css
/* 字体类定义 */
.font-inter { font-family: 'Inter', sans-serif; }
.font-playfair { font-family: 'Playfair Display', serif; }

/* 中文字体优化 */
@supports (font-variation-settings: normal) {
  .font-inter { font-family: 'Inter var', sans-serif; }
}
```

### 动画资源

#### Lottie 动画 (可选)
```typescript
// 推荐 LottieFiles 动画
const lottieAnimations = {
  loading: 'https://assets5.lottiefiles.com/packages/lf20_loading.json',
  success: 'https://assets5.lottiefiles.com/packages/lf20_success.json',
  magic: 'https://assets5.lottiefiles.com/packages/lf20_magic.json'
};

// React Lottie 集成
import Lottie from 'react-lottie-player';

const LoadingAnimation = () => (
  <Lottie
    loop
    animationData={lottieAnimations.loading}
    play
    style={{ width: 150, height: 150 }}
  />
);
```

### 纹理和图案

#### CSS 图案生成
```css
/* 几何图案背景 */
.pattern-dots {
  background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pattern-grid {
  background-image:
    linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 30px 30px;
}

.pattern-hexagon {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
```

---

## 📝 当前进度
- ✅ **阶段1**: 结构分析与理解确认
- ✅ **阶段2**: 设计系统定义
- ✅ **阶段3**: Layout重构方案
- ✅ **阶段4**: 交互微动效定义
- ✅ **阶段6**: 素材资源建议
- ⏳ **阶段5**: 完整代码实现 (准备开始)

---

*文档将在每个设计阶段完成后持续更新...*
