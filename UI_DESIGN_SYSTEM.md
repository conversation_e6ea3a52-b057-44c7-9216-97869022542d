# 六爻排盘网站 UI/UX 设计系统重构文档
# FortuneTeller.Today UI/UX Design System Redesign

## 📋 项目概述

### 当前网站结构分析
- **技术栈**: React + TypeScript + Tailwind CSS
- **主题系统**: 三种风格主题 (modern/cute/classic)
- **核心功能**: 六爻占卜、AI分析、用户认证、主题切换
- **响应式**: 基础移动端适配，需要优化

### 🚫 设计约束原则
- **绝不修改业务逻辑**: 保持所有JavaScript交互行为不变
- **仅限UI层面**: 布局、样式、视觉、交互动画优化
- **保持功能完整**: 导航、表单提交、Modal等核心功能不变

---

## 🎨 设计系统定义

### 颜色系统
基于现有CSS变量系统，优化三主题配色：

#### Modern Theme (科技风)
```css
:root {
  --background: 240 10% 4%;           /* 深色背景 */
  --foreground: 210 40% 95%;          /* 浅色文字 */
  --primary: 230 40% 20%;             /* 主色调 */
  --secondary: 45 90% 55%;            /* 金色强调 */
  --accent: 270 50% 25%;              /* 紫色点缀 */
}
```

#### Cute Theme (暖色风)
```css
:root {
  --primary: 340 75% 55%;             /* 粉色主调 */
  --secondary: 45 95% 60%;            /* 暖黄色 */
  --accent: 280 60% 65%;              /* 淡紫色 */
}
```

#### Classic Theme (国学风)
```css
:root {
  --primary: 25 75% 45%;              /* 古铜色 */
  --secondary: 45 85% 50%;            /* 金黄色 */
  --accent: 15 60% 35%;               /* 深棕色 */
}
```

### 字体系统
```css
/* 主字体栈 */
.font-primary {
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 中文优化字体 */
.font-chinese {
  font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 经典主题衬线字体 */
.font-classic {
  font-family: 'Playfair Display', 'Times New Roman', serif;
}
```

### Spacing Scale (基于4px)
```css
.spacing-system {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
}
```

---

## 🏗️ Layout 重构方案

### Container 系统
```css
/* 统一容器宽度 */
.main-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* 内容区域容器 */
.content-container {
  @apply max-w-4xl mx-auto;
}

/* 宽内容容器 */
.wide-container {
  @apply max-w-6xl mx-auto;
}
```

### 响应式断点策略
- **Mobile**: < 640px (单列布局，紧凑间距)
- **Tablet**: 640px - 1024px (2列布局，中等间距)
- **Desktop**: > 1024px (3列布局，宽松间距)

### Header 布局重构
```tsx
// 优化后的Header布局
<header className="sticky top-0 z-50 border-b border-border/50 bg-card/80 backdrop-blur-md">
  <div className="main-container">
    <div className="grid grid-cols-[1fr_auto] lg:grid-cols-[auto_1fr_auto] gap-4 py-4 items-center">
      {/* Logo区域 */}
      <div className="flex items-center space-x-3">
        <h1 className="text-2xl font-bold gradient-text cursor-pointer">
          {t('appName')}
        </h1>
        <Badge variant="secondary" className="text-xs hidden sm:inline-flex">
          {t('aiAnalysis')}
        </Badge>
      </div>
      
      {/* 桌面端导航 */}
      <nav className="hidden lg:flex items-center justify-center space-x-8">
        <Button variant="ghost" className="text-sm font-medium">
          {t('features')}
        </Button>
        <Button variant="ghost" className="text-sm font-medium">
          {t('pricing')}
        </Button>
      </nav>
      
      {/* 右侧工具栏 */}
      <div className="flex items-center space-x-3">
        <LanguageSwitcher />
        <ThemeSwitcher />
        {/* 移动端菜单按钮 */}
        <Button variant="ghost" size="sm" className="lg:hidden">
          <Menu className="h-5 w-5" />
        </Button>
        {/* 用户菜单保持不变 */}
      </div>
    </div>
  </div>
</header>
```

### Hero 区域重构
```tsx
// 优化后的Hero布局 - Modern主题
<section className="relative min-h-screen flex items-center justify-center overflow-hidden">
  {/* 背景层 */}
  <div 
    className="absolute inset-0 bg-cover bg-center bg-fixed"
    style={{
      backgroundImage: `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url(${modernBg})`,
    }}
  />
  
  {/* 内容层 */}
  <div className="relative z-10 main-container text-center">
    <div className="content-container space-y-8 sm:space-y-12">
      {/* 标题区域 */}
      <div className="space-y-6">
        <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold gradient-text leading-tight">
          {getThemeTitle()}
        </h1>
        <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
          {getThemeDescription()}
        </p>
      </div>
      
      {/* CTA按钮区域 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <Button 
          variant="golden" 
          size="lg" 
          className="text-lg px-8 sm:px-12 py-4 sm:py-6 rounded-xl hover:scale-105 transition-transform duration-300"
        >
          <Zap className="w-5 h-5 mr-2" />
          {t('startAiAnalysis')}
        </Button>
        <Button 
          variant="outline" 
          size="lg" 
          className="text-lg px-8 py-4 rounded-xl hover:bg-card/50 transition-colors duration-300"
        >
          {t('learnMore')}
        </Button>
      </div>
    </div>
  </div>
  
  {/* 滚动指示器 */}
  <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <ChevronDown className="w-6 h-6 text-muted-foreground" />
  </div>
</section>
```

### Features 卡片区域重构
```tsx
// 优化后的Features布局
<section className="section-spacing">
  <div className="main-container">
    {/* 标题区域 */}
    <div className="text-center content-spacing mb-12 lg:mb-16">
      <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold gradient-text mb-6">
        {t('coreFeaturesHighlight')}
      </h2>
      <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
        {t('professionalAnalysisDesc')}
      </p>
    </div>
    
    {/* 卡片网格 */}
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 auto-rows-fr">
      {features.map((feature, index) => (
        <DivinationCard 
          key={index}
          title={feature.title}
          className="hover:scale-[1.02] transition-transform duration-300"
        >
          {feature.content}
        </DivinationCard>
      ))}
    </div>
  </div>
</section>
```

---

---

## 🎭 交互微动效定义

### 动画时长系统
```css
:root {
  --transition-fast: 150ms ease-out;
  --transition-normal: 300ms ease-out;
  --transition-slow: 500ms ease-out;
  --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
```

### 按钮交互效果

#### CTA主按钮 (mystical-button)
```css
.mystical-button {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
  background: var(--gradient-primary);
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.mystical-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 20px var(--primary);
}

.mystical-button:active {
  transform: translateY(0) scale(0.98);
  transition: var(--transition-fast);
}

.mystical-button:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}
```

#### 次要按钮
```css
.secondary-button {
  @apply border border-border bg-card hover:bg-muted transition-all duration-300;
}

.secondary-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
```

### 卡片交互效果

#### DivinationCard 增强
```css
.divination-card {
  @apply rounded-lg border border-border bg-card shadow-lg;
  transition: var(--transition-normal);
  transform: translateY(0) scale(1);
}

.divination-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15), 0 0 0 1px var(--border);
}

/* Cute主题特殊效果 */
.theme-cute .divination-card:hover {
  transform: translateY(-2px) scale(1.03) rotate(0.5deg);
  box-shadow: 0 8px 32px var(--primary);
}
```

### 表单输入框交互
```css
.form-input {
  @apply w-full px-4 py-3 rounded-lg border border-input bg-background;
  transition: var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 3px rgba(var(--ring), 0.1);
  transform: scale(1.01);
}

.form-input:focus-visible {
  ring: 2px solid var(--ring);
  ring-offset: 2px;
}
```

### 滚动动画效果

#### Fade-in on Scroll
```css
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-fade-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* 延迟动画 */
.scroll-fade-in-delay-1 { animation-delay: 0.1s; }
.scroll-fade-in-delay-2 { animation-delay: 0.2s; }
.scroll-fade-in-delay-3 { animation-delay: 0.3s; }
```

#### 视差滚动效果
```css
.parallax-bg {
  transform: translateZ(0);
  will-change: transform;
}

/* JavaScript控制 */
.parallax-bg[data-scroll] {
  transform: translate3d(0, calc(var(--scroll-y) * 0.5px), 0);
}
```

### Loading 状态动画
```css
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 5px var(--primary);
  }
  50% {
    opacity: 0.7;
    box-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary);
  }
}

.loading-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}
```

### Toast 通知动画
```css
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.toast-enter {
  animation: slideInRight 0.3s ease-out;
}
```

### 主题切换动画
```css
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 切换时的淡入效果 */
@keyframes themeChange {
  0% { opacity: 0.8; }
  50% { opacity: 0.9; }
  100% { opacity: 1; }
}

.theme-changing {
  animation: themeChange 0.5s ease-out;
}
```

---

---

## 🎨 素材资源建议

### 背景图片资源

#### Modern Theme (科技风)
```typescript
// 推荐高质量背景图
const modernBackgrounds = {
  hero: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&q=80', // 科技网格
  secondary: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1920&q=80', // 星空科技
  pattern: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=1920&q=80'  // 抽象几何
};

// CSS渐变叠加
const modernGradients = {
  primary: 'linear-gradient(135deg, rgba(15,23,42,0.9) 0%, rgba(30,41,59,0.8) 100%)',
  hero: 'linear-gradient(rgba(0,0,0,0.7), rgba(15,23,42,0.6))',
  card: 'linear-gradient(145deg, rgba(30,41,59,0.95), rgba(15,23,42,0.9))'
};
```

#### Cute Theme (暖色风)
```typescript
const cuteBackgrounds = {
  hero: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1920&q=80', // 粉色云朵
  secondary: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80', // 温暖渐变
  pattern: 'https://images.unsplash.com/photo-1557682250-33bd709cbe85?w=1920&q=80'  // 柔和抽象
};

const cuteGradients = {
  primary: 'linear-gradient(135deg, rgba(255,182,193,0.9) 0%, rgba(255,218,185,0.8) 100%)',
  hero: 'linear-gradient(rgba(255,182,193,0.8), rgba(255,218,185,0.7))',
  card: 'linear-gradient(145deg, rgba(255,240,245,0.95), rgba(255,228,225,0.9))'
};
```

#### Classic Theme (国学风)
```typescript
const classicBackgrounds = {
  hero: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80', // 古典纹理
  secondary: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&q=80', // 山水意境
  pattern: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80'  // 传统图案
};

const classicGradients = {
  primary: 'linear-gradient(135deg, rgba(139,69,19,0.9) 0%, rgba(160,82,45,0.8) 100%)',
  hero: 'linear-gradient(rgba(139,69,19,0.8), rgba(160,82,45,0.6))',
  card: 'linear-gradient(145deg, rgba(222,184,135,0.95), rgba(205,133,63,0.9))'
};
```

### 图标系统

#### 推荐图标库
```typescript
// 使用 Lucide React (已集成)
import {
  Zap, TrendingUp, Shield, Users, Star, Heart,
  Sparkles, Sun, Moon, Palette, Menu, X,
  ChevronDown, ChevronRight, ArrowRight,
  CheckCircle, XCircle, AlertCircle, Info
} from 'lucide-react';

// 主题特色图标映射
const themeIcons = {
  modern: {
    primary: Zap,
    secondary: TrendingUp,
    accent: Shield
  },
  cute: {
    primary: Heart,
    secondary: Star,
    accent: Sparkles
  },
  classic: {
    primary: Sun,
    secondary: Scroll,
    accent: Flower
  }
};
```

#### 自定义SVG图标
```tsx
// 六爻专用图标
const HexagramIcon = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6">
    <path d="M3 6h18M3 12h18M3 18h18" stroke="currentColor" strokeWidth="2"/>
    <circle cx="21" cy="6" r="1" fill="currentColor"/>
    <circle cx="21" cy="18" r="1" fill="currentColor"/>
  </svg>
);

const DivinationIcon = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6">
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/>
    <path d="M12 2v20M2 12h20" stroke="currentColor" strokeWidth="1"/>
  </svg>
);
```

### 插图资源

#### unDraw 插图风格
```typescript
// 推荐 unDraw 插图 (可自定义颜色)
const illustrations = {
  hero: 'https://undraw.co/api/illustrations/fortune_teller',
  features: 'https://undraw.co/api/illustrations/crystal_ball',
  empty_state: 'https://undraw.co/api/illustrations/void',
  success: 'https://undraw.co/api/illustrations/celebration'
};

// 自定义颜色匹配主题
const illustrationColors = {
  modern: '#1e293b',
  cute: '#ec4899',
  classic: '#92400e'
};
```

### 字体资源

#### Google Fonts 集成
```html
<!-- 在 index.html 中添加 -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
```

```css
/* 字体类定义 */
.font-inter { font-family: 'Inter', sans-serif; }
.font-playfair { font-family: 'Playfair Display', serif; }

/* 中文字体优化 */
@supports (font-variation-settings: normal) {
  .font-inter { font-family: 'Inter var', sans-serif; }
}
```

### 动画资源

#### Lottie 动画 (可选)
```typescript
// 推荐 LottieFiles 动画
const lottieAnimations = {
  loading: 'https://assets5.lottiefiles.com/packages/lf20_loading.json',
  success: 'https://assets5.lottiefiles.com/packages/lf20_success.json',
  magic: 'https://assets5.lottiefiles.com/packages/lf20_magic.json'
};

// React Lottie 集成
import Lottie from 'react-lottie-player';

const LoadingAnimation = () => (
  <Lottie
    loop
    animationData={lottieAnimations.loading}
    play
    style={{ width: 150, height: 150 }}
  />
);
```

### 纹理和图案

#### CSS 图案生成
```css
/* 几何图案背景 */
.pattern-dots {
  background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pattern-grid {
  background-image:
    linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 30px 30px;
}

.pattern-hexagon {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
```

---

---

## 💻 完整代码实现

### 1. 增强的CSS样式系统

#### 更新 src/index.css
```css
/* 在现有样式基础上添加以下增强样式 */

/* 新增动画时长系统 */
:root {
  --transition-fast: 150ms ease-out;
  --transition-normal: 300ms ease-out;
  --transition-slow: 500ms ease-out;
  --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 增强的按钮样式 */
.mystical-button-enhanced {
  @apply px-6 py-3 rounded-lg font-medium relative overflow-hidden;
  background: var(--gradient-primary);
  color: hsl(var(--primary-foreground));
  transform: translateY(0) scale(1);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  transition: all var(--transition-normal);
}

.mystical-button-enhanced:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 20px hsl(var(--primary) / 0.4);
}

.mystical-button-enhanced:active {
  transform: translateY(0) scale(0.98);
  transition: var(--transition-fast);
}

.mystical-button-enhanced:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* 增强的卡片样式 */
.divination-card-enhanced {
  @apply rounded-lg border border-border bg-card text-card-foreground shadow-lg relative;
  background: var(--gradient-card);
  transform: translateY(0) scale(1);
  transition: all var(--transition-normal);
}

.divination-card-enhanced:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15), 0 0 0 1px hsl(var(--border));
}

/* Cute主题特殊效果 */
.theme-cute .divination-card-enhanced:hover {
  transform: translateY(-2px) scale(1.03) rotate(0.5deg);
  box-shadow: 0 8px 32px hsl(var(--primary) / 0.2);
}

/* 表单输入框增强 */
.form-input-enhanced {
  @apply w-full px-4 py-3 rounded-lg border border-input bg-background text-foreground;
  transition: all var(--transition-normal);
}

.form-input-enhanced:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 3px hsl(var(--ring) / 0.1);
  transform: scale(1.01);
}

.form-input-enhanced:focus-visible {
  ring: 2px solid hsl(var(--ring));
  ring-offset: 2px;
}

/* 滚动动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-fade-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

.scroll-fade-in-delay-1 { animation-delay: 0.1s; }
.scroll-fade-in-delay-2 { animation-delay: 0.2s; }
.scroll-fade-in-delay-3 { animation-delay: 0.3s; }

/* 视差滚动 */
.parallax-bg {
  transform: translateZ(0);
  will-change: transform;
}

/* Loading动画 */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 5px hsl(var(--primary));
  }
  50% {
    opacity: 0.7;
    box-shadow: 0 0 20px hsl(var(--primary)), 0 0 30px hsl(var(--primary));
  }
}

.loading-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 纹理图案 */
.pattern-dots {
  background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pattern-grid {
  background-image:
    linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 30px 30px;
}

/* 主题切换动画 */
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes themeChange {
  0% { opacity: 0.8; }
  50% { opacity: 0.9; }
  100% { opacity: 1; }
}

.theme-changing {
  animation: themeChange 0.5s ease-out;
}
```

### 2. 增强的Header组件

#### 更新 src/components/Header.tsx
```tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { User, LogIn, LogOut, UserCircle, Menu, X } from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';
import { AuthModal } from './AuthModal';
import { ThemeSwitcher } from './ThemeSwitcher';
import { LanguageSwitcher } from './LanguageSwitcher';
import { useTranslation } from '@/hooks/useTranslation';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

export const Header: React.FC = () => {
  const { user, loading, signOut } = useAuthStore();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSignOut = async () => {
    await signOut();
    setMobileMenuOpen(false);
  };

  const handleProfileClick = () => {
    navigate('/profile');
    setMobileMenuOpen(false);
  };

  const handleNavClick = (path: string) => {
    navigate(path);
    setMobileMenuOpen(false);
  };

  return (
    <>
      <header className="sticky top-0 z-50 border-b border-border/50 bg-card/80 backdrop-blur-md theme-transition">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-[1fr_auto] lg:grid-cols-[auto_1fr_auto] gap-4 py-4 items-center">

            {/* Logo区域 */}
            <div className="flex items-center space-x-3">
              <h1
                className="text-2xl font-bold gradient-text cursor-pointer hover:scale-105 transition-transform duration-300"
                onClick={() => handleNavClick('/')}
              >
                {t('appName')}
              </h1>
              <Badge variant="secondary" className="text-xs hidden sm:inline-flex">
                {t('aiAnalysis')}
              </Badge>
            </div>

            {/* 桌面端导航 */}
            <nav className="hidden lg:flex items-center justify-center space-x-8">
              <Button
                variant="ghost"
                className="text-sm font-medium hover:scale-105 transition-transform duration-300"
                onClick={() => handleNavClick('/features')}
              >
                {t('features')}
              </Button>
              <Button
                variant="ghost"
                className="text-sm font-medium hover:scale-105 transition-transform duration-300"
                onClick={() => handleNavClick('/pricing')}
              >
                {t('pricing')}
              </Button>
            </nav>

            {/* 右侧工具栏 */}
            <div className="flex items-center space-x-3">
              <div className="hidden sm:flex items-center space-x-3">
                <LanguageSwitcher />
                <ThemeSwitcher />
              </div>

              {/* 移动端菜单按钮 */}
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden hover:scale-105 transition-transform duration-300"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>

              {/* 用户菜单 */}
              <div className="hidden lg:block">
                {loading ? (
                  <Button variant="ghost" disabled className="loading-pulse">
                    {t('loading')}
                  </Button>
                ) : user ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="text-sm hover:scale-105 transition-transform duration-300">
                        {t('profile')}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="theme-transition">
                      <DropdownMenuItem onClick={handleProfileClick}>
                        <UserCircle className="h-4 w-4 mr-2" />
                        {t('profilePage')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleSignOut}>
                        <LogOut className="h-4 w-4 mr-2" />
                        {t('logout')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Button
                    variant="default"
                    onClick={() => setAuthModalOpen(true)}
                    className="mystical-button-enhanced"
                  >
                    {t('login')}
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* 移动端菜单 */}
          {mobileMenuOpen && (
            <div className="lg:hidden border-t border-border/50 py-4 space-y-4 theme-transition">
              <div className="flex flex-col space-y-3">
                <Button
                  variant="ghost"
                  className="justify-start"
                  onClick={() => handleNavClick('/features')}
                >
                  {t('features')}
                </Button>
                <Button
                  variant="ghost"
                  className="justify-start"
                  onClick={() => handleNavClick('/pricing')}
                >
                  {t('pricing')}
                </Button>

                <div className="flex items-center space-x-3 px-4">
                  <LanguageSwitcher />
                  <ThemeSwitcher />
                </div>

                {loading ? (
                  <Button variant="ghost" disabled className="justify-start loading-pulse">
                    {t('loading')}
                  </Button>
                ) : user ? (
                  <div className="space-y-2">
                    <Button variant="ghost" className="justify-start" onClick={handleProfileClick}>
                      <UserCircle className="h-4 w-4 mr-2" />
                      {t('profilePage')}
                    </Button>
                    <Button variant="ghost" className="justify-start" onClick={handleSignOut}>
                      <LogOut className="h-4 w-4 mr-2" />
                      {t('logout')}
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="default"
                    className="mystical-button-enhanced mx-4"
                    onClick={() => setAuthModalOpen(true)}
                  >
                    {t('login')}
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </header>

      <AuthModal open={authModalOpen} onOpenChange={setAuthModalOpen} />
    </>
  );
};
```

### 3. 增强的DivinationCard组件

#### 更新 src/components/DivinationCard.tsx
```tsx
import React, { useEffect, useRef, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useThemeStore } from '@/stores/themeStore';
import { Heart, Star, Sparkles } from 'lucide-react';

interface DivinationCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  glowing?: boolean;
  delay?: number; // 滚动动画延迟
}

export const DivinationCard: React.FC<DivinationCardProps> = ({
  title,
  children,
  className,
  glowing = false,
  delay = 0
}) => {
  const { currentTheme } = useThemeStore();
  const cardRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  // 滚动动画观察器
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delay * 100);
        }
      },
      { threshold: 0.1 }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, [delay]);

  const getCuteDecorations = () => {
    if (currentTheme !== 'cute') return null;

    return (
      <div className="absolute -top-1 -right-1 flex space-x-1">
        <Star className="w-3 h-3 text-accent animate-pulse" />
        <Heart className="w-3 h-3 text-primary animate-bounce" />
      </div>
    );
  };

  if (currentTheme === 'cute') {
    return (
      <Card
        ref={cardRef}
        className={cn(
          "cute-card relative overflow-hidden transition-all duration-300",
          "bg-card border-2 border-primary/30 shadow-lg rounded-2xl",
          "divination-card-enhanced",
          glowing && "glow-border",
          isVisible ? "scroll-fade-in" : "opacity-0",
          className
        )}
        style={{ animationDelay: `${delay * 0.1}s` }}
      >
        {getCuteDecorations()}
        <CardHeader className="bg-gradient-to-r from-primary/15 to-secondary/15 rounded-t-2xl pb-3">
          <CardTitle className="text-primary font-bold text-lg flex items-center justify-center gap-2">
            <Sparkles className="w-4 h-4 text-primary" />
            {title}
            <Heart className="w-4 h-4 text-accent" />
          </CardTitle>
        </CardHeader>
        <CardContent className="bg-card/90 text-cardForeground p-4 rounded-b-2xl">
          {children}
        </CardContent>
      </Card>
    );
  }

  // 其他主题的增强样式
  return (
    <Card
      ref={cardRef}
      className={cn(
        "divination-card-enhanced",
        glowing && "glow-border",
        isVisible ? "scroll-fade-in" : "opacity-0",
        className
      )}
      style={{ animationDelay: `${delay * 0.1}s` }}
    >
      <CardHeader className="pb-4">
        <CardTitle className="gradient-text text-xl font-bold">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
};
```

### 4. 增强的Hero区域组件

#### 创建 src/components/HeroSection.tsx
```tsx
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { Zap, ChevronDown, Star, Heart, Scroll } from 'lucide-react';
import { cn } from '@/lib/utils';

// 背景图片资源
const backgroundImages = {
  modern: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&q=80',
  cute: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1920&q=80',
  classic: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80'
};

interface HeroSectionProps {
  onCTAClick?: () => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ onCTAClick }) => {
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();
  const [scrollY, setScrollY] = useState(0);

  // 视差滚动效果
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const getThemeTitle = () => {
    switch (currentTheme) {
      case 'modern': return t('modernTitle') || 'AI-Powered Fortune Telling';
      case 'cute': return t('cuteTitle') || '✨ Magical Fortune Reading ✨';
      case 'classic': return t('classicTitle') || '易经六爻 · 智慧占卜';
      default: return t('appName');
    }
  };

  const getThemeDescription = () => {
    switch (currentTheme) {
      case 'modern': return t('modernDesc') || 'Advanced AI analysis meets ancient wisdom';
      case 'cute': return t('cuteDesc') || 'Discover your destiny with love and magic';
      case 'classic': return t('classicDesc') || '传承千年智慧，洞察人生玄机';
      default: return t('appDescription');
    }
  };

  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      case 'classic': return Scroll;
      default: return Zap;
    }
  };

  const ThemeIcon = getThemeIcon();

  const handleScrollToContent = () => {
    document.getElementById('divination-interface')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  // Modern主题Hero
  if (currentTheme === 'modern') {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* 视差背景层 */}
        <div
          className="absolute inset-0 bg-cover bg-center parallax-bg pattern-grid"
          style={{
            backgroundImage: `linear-gradient(rgba(0,0,0,0.7), rgba(15,23,42,0.6)), url(${backgroundImages.modern})`,
            transform: `translate3d(0, ${scrollY * 0.5}px, 0)`
          }}
        />

        {/* 内容层 */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8 sm:space-y-12">
            {/* 图标动画 */}
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center shadow-2xl hover:scale-110 transition-transform duration-300">
                <ThemeIcon className="w-10 h-10 text-white" />
              </div>
            </div>

            {/* 标题区域 */}
            <div className="space-y-6 scroll-fade-in">
              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold gradient-text leading-tight">
                {getThemeTitle()}
              </h1>
              <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {getThemeDescription()}
              </p>
            </div>

            {/* CTA按钮区域 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center scroll-fade-in-delay-1">
              <Button
                variant="golden"
                size="lg"
                className="mystical-button-enhanced text-lg px-8 sm:px-12 py-4 sm:py-6 rounded-xl"
                onClick={onCTAClick || handleScrollToContent}
              >
                <Zap className="w-5 h-5 mr-2" />
                {t('startAiAnalysis')}
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 rounded-xl hover:bg-card/50 transition-all duration-300 hover:scale-105"
              >
                {t('learnMore') || 'Learn More'}
              </Button>
            </div>
          </div>
        </div>

        {/* 滚动指示器 */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce cursor-pointer" onClick={handleScrollToContent}>
          <ChevronDown className="w-6 h-6 text-muted-foreground hover:text-foreground transition-colors" />
        </div>
      </section>
    );
  }

  // Cute主题Hero
  if (currentTheme === 'cute') {
    return (
      <section
        className="py-24 px-6 relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(255,182,193,0.9), rgba(255,218,185,0.8)), url(${backgroundImages.cute})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div className="max-w-4xl mx-auto text-center space-y-12">
          <div className="space-y-8 scroll-fade-in">
            <div className="flex justify-center space-x-4 mb-8">
              <Star className="w-8 h-8 text-yellow-400 animate-pulse" />
              <Heart className="w-10 h-10 text-pink-400 animate-bounce" />
              <Star className="w-8 h-8 text-yellow-400 animate-pulse" />
            </div>

            <h1 className="text-5xl md:text-6xl font-bold gradient-text leading-tight" style={{textShadow: '2px 2px 4px rgba(0,0,0,0.1)'}}>
              {getThemeTitle()}
            </h1>

            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border-4 border-pink-200">
              <p className="text-lg md:text-xl text-pink-700 leading-relaxed">
                {getThemeDescription()}
              </p>
            </div>
          </div>

          <div className="scroll-fade-in-delay-1">
            <Button
              variant="golden"
              size="lg"
              className="mystical-button-enhanced text-lg px-12 py-6 rounded-3xl"
              onClick={onCTAClick || handleScrollToContent}
            >
              <Heart className="w-5 h-5 mr-2" />
              {t('startAiAnalysis')}
            </Button>
          </div>
        </div>
      </section>
    );
  }

  // Classic主题Hero
  return (
    <section
      className="py-32 px-6 relative overflow-hidden"
      style={{
        backgroundImage: `linear-gradient(rgba(139,69,19,0.8), rgba(160,82,45,0.6)), url(${backgroundImages.classic})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <div className="max-w-5xl mx-auto text-center space-y-16">
        <div className="space-y-8 scroll-fade-in">
          <div className="flex justify-center mb-8">
            <div className="w-32 h-32 bg-gradient-to-br from-amber-600 to-orange-700 rounded-full flex items-center justify-center border-4 border-amber-400 shadow-2xl hover:scale-110 transition-transform duration-300">
              <Scroll className="w-16 h-16 text-white" />
            </div>
          </div>

          <h1 className="text-6xl md:text-7xl font-bold gradient-text leading-tight" style={{fontFamily: 'serif', textShadow: '3px 3px 6px rgba(0,0,0,0.3)'}}>
            {getThemeTitle()}
          </h1>

          <div className="bg-amber-50/90 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border-2 border-amber-300">
            <p className="text-xl md:text-2xl text-amber-900 leading-relaxed" style={{fontFamily: 'serif'}}>
              {getThemeDescription()}
            </p>
          </div>
        </div>

        <div className="scroll-fade-in-delay-1">
          <Button
            variant="golden"
            size="lg"
            className="mystical-button-enhanced text-xl px-16 py-8 rounded-2xl"
            onClick={onCTAClick || handleScrollToContent}
          >
            <Scroll className="w-6 h-6 mr-3" />
            {t('startAiAnalysis')}
          </Button>
        </div>
      </div>
    </section>
  );
};
```

### 5. 增强的Features区域组件

#### 创建 src/components/FeaturesSection.tsx
```tsx
import React from 'react';
import { DivinationCard } from './DivinationCard';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { TrendingUp, Heart, Scroll, Shield, Users, Zap, Star, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

export const FeaturesSection: React.FC = () => {
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();

  const getFeatures = () => {
    const baseFeatures = [
      {
        id: 'planning',
        modern: {
          title: t('intelligentPlanning') || 'AI-Powered Analysis',
          icon: TrendingUp,
          description: 'Advanced algorithms analyze your divination with precision and insight.'
        },
        cute: {
          title: t('easyPlanning') || '✨ Magical Insights ✨',
          icon: Heart,
          description: 'Discover your path with love, care, and magical wisdom!'
        },
        classic: {
          title: t('traditionalPlanning') || '传统智慧分析',
          icon: Scroll,
          description: '结合千年易经智慧，为您提供深度解读与指导。'
        }
      },
      {
        id: 'accuracy',
        modern: {
          title: 'High Precision Results',
          icon: Shield,
          description: 'State-of-the-art AI ensures accurate and reliable fortune readings.'
        },
        cute: {
          title: '💖 Loving Guidance 💖',
          icon: Star,
          description: 'Gentle and caring advice to help you navigate life\'s journey.'
        },
        classic: {
          title: '精准卦象解读',
          icon: Zap,
          description: '专业的卦象分析，准确把握时机与趋势。'
        }
      },
      {
        id: 'community',
        modern: {
          title: 'Smart Recommendations',
          icon: Users,
          description: 'Personalized insights based on advanced pattern recognition.'
        },
        cute: {
          title: '🌟 Friendship & Support 🌟',
          icon: Sparkles,
          description: 'Join a community of like-minded souls on their spiritual journey.'
        },
        classic: {
          title: '专业咨询服务',
          icon: Users,
          description: '资深易学专家提供个性化指导与建议。'
        }
      }
    ];

    return baseFeatures.map(feature => ({
      ...feature[currentTheme as keyof typeof feature],
      id: feature.id
    }));
  };

  const features = getFeatures();

  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        <div className="text-center mb-12 lg:mb-16 scroll-fade-in">
          <h2 className={cn(
            "text-3xl sm:text-4xl lg:text-5xl font-bold gradient-text mb-6",
            currentTheme === 'cute' && 'text-pink-600'
          )}>
            {t('coreFeaturesHighlight') || 'Core Features'}
          </h2>
          <p className={cn(
            "text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",
            currentTheme === 'cute' && 'text-pink-500'
          )}>
            {t('professionalAnalysisDesc') || 'Professional analysis powered by advanced AI technology'}
          </p>
        </div>

        {/* 特性卡片网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 auto-rows-fr">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;

            return (
              <DivinationCard
                key={feature.id}
                title={feature.title}
                delay={index}
                className="h-full"
              >
                <div className="space-y-4 h-full flex flex-col">
                  <div className="text-center py-4">
                    <div className={cn(
                      "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center transition-transform duration-300 hover:scale-110",
                      currentTheme === 'cute' ?
                        "bg-gradient-to-br from-primary/30 to-secondary/30" :
                        "bg-gradient-to-br from-primary to-accent"
                    )}>
                      <IconComponent className={cn(
                        "w-8 h-8",
                        currentTheme === 'cute' ? "text-primary" : "text-white"
                      )} />
                    </div>
                  </div>

                  <div className="flex-1 flex items-center">
                    <p className={cn(
                      "text-sm text-center leading-relaxed",
                      currentTheme === 'cute' ?
                        "text-pink-600" :
                        "text-muted-foreground"
                    )}>
                      {feature.description}
                    </p>
                  </div>
                </div>
              </DivinationCard>
            );
          })}
        </div>
      </div>
    </section>
  );
};
```

### 6. 主页面集成更新

#### 更新 src/pages/Index.tsx 的关键部分
```tsx
// 在现有Index.tsx中替换相关部分

import React, { useEffect, useState } from 'react';
import { DivinationInterface } from '@/components/DivinationInterface';
import { Header } from '@/components/Header';
import { HeroSection } from '@/components/HeroSection'; // 新增
import { FeaturesSection } from '@/components/FeaturesSection'; // 新增
import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { SavedRecordsPanel } from '@/components/SavedRecordsPanel';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useAuthStore } from '@/stores/authStore';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';

const Index = () => {
  const { user } = useAuthStore();
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();
  const [isLoaded, setIsLoaded] = useState(false);

  // 页面加载动画
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const handleCTAClick = () => {
    document.getElementById('divination-interface')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  return (
    <div className={cn(
      "min-h-screen theme-transition",
      isLoaded ? "theme-changing" : "opacity-0"
    )}>
      <Header />

      {/* 使用新的Hero组件替换原有的三个主题Hero区域 */}
      <HeroSection onCTAClick={handleCTAClick} />

      {/* Main Interface */}
      <div id="divination-interface" className="py-16 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="max-w-4xl mx-auto">
            <DivinationInterface />
          </div>
        </div>
      </div>

      {/* 使用新的Features组件替换原有的Features Section */}
      <FeaturesSection />

      {/* 保持原有的其他区域不变 */}
      {/* ... 其他现有内容 ... */}
    </div>
  );
};

export default Index;
```

### 7. 滚动动画增强脚本

#### 创建 src/hooks/useScrollAnimation.ts
```typescript
import { useEffect, useRef, useState } from 'react';

interface UseScrollAnimationOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

export const useScrollAnimation = (options: UseScrollAnimationOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '0px',
    triggerOnce = true
  } = options;

  const elementRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (triggerOnce && elementRef.current) {
            observer.unobserve(elementRef.current);
          }
        } else if (!triggerOnce) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin, triggerOnce]);

  return { elementRef, isVisible };
};
```

### 8. 视差滚动增强脚本

#### 创建 src/hooks/useParallax.ts
```typescript
import { useEffect, useState } from 'react';

export const useParallax = (speed: number = 0.5) => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return {
    transform: `translate3d(0, ${scrollY * speed}px, 0)`
  };
};
```

---

## 📝 当前进度
- ✅ **阶段1**: 结构分析与理解确认
- ✅ **阶段2**: 设计系统定义
- ✅ **阶段3**: Layout重构方案
- ✅ **阶段4**: 交互微动效定义
- ✅ **阶段6**: 素材资源建议
- ✅ **阶段5**: 完整代码实现

---

## 🎯 实现总结

### ✨ 已完成的UI/UX增强：

1. **设计系统完善**
   - 统一的颜色、字体、间距系统
   - 三主题适配的视觉风格
   - 响应式断点策略

2. **交互动效升级**
   - 按钮hover/focus/active状态
   - 卡片悬浮和缩放效果
   - 滚动进入动画
   - 视差背景滚动

3. **组件功能增强**
   - Header: 移动端菜单、更好的导航
   - Hero: 三主题统一组件、视差效果
   - Cards: 滚动动画、主题特色效果
   - Features: 响应式网格、图标动画

4. **无障碍和体验**
   - Focus ring指示器
   - 键盘导航支持
   - 加载状态动画
   - 平滑滚动过渡

5. **性能优化**
   - CSS transform动画
   - Intersection Observer
   - 被动事件监听
   - 组件懒加载

### 🚀 使用说明：
1. 将CSS样式添加到 `src/index.css`
2. 更新现有组件文件
3. 创建新的组件文件
4. 集成到主页面中

所有修改都**仅限UI层面**，**未触及任何业务逻辑**！

---

*UI/UX设计系统重构完成！*
