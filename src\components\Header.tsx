import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { User, LogIn, LogOut, UserCircle, Menu, X } from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';
import { AuthModal } from './AuthModal';
import { ThemeSwitcher } from './ThemeSwitcher';
import { LanguageSwitcher } from './LanguageSwitcher';
import { useTranslation } from '@/hooks/useTranslation';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

export const Header: React.FC = () => {
  const { user, loading, signOut } = useAuthStore();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSignOut = async () => {
    await signOut();
    setMobileMenuOpen(false);
  };

  const handleProfileClick = () => {
    navigate('/profile');
    setMobileMenuOpen(false);
  };

  const handleNavClick = (path: string) => {
    navigate(path);
    setMobileMenuOpen(false);
  };

  return (
    <>
      <header className="sticky top-0 z-50 border-b border-border/50 bg-card/80 backdrop-blur-md theme-transition">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-[1fr_auto] lg:grid-cols-[auto_1fr_auto] gap-4 py-4 items-center">

            {/* Logo区域 */}
            <div className="flex items-center space-x-3">
              <h1
                className="text-2xl font-bold gradient-text cursor-pointer hover:scale-105 transition-transform duration-300"
                onClick={() => handleNavClick('/')}
              >
                {t('appName')}
              </h1>
              <Badge variant="secondary" className="text-xs hidden sm:inline-flex">
                {t('aiAnalysis')}
              </Badge>
            </div>

            {/* 桌面端导航 */}
            <nav className="hidden lg:flex items-center justify-center space-x-8">
              <Button
                variant="ghost"
                className="text-sm font-medium hover:scale-105 transition-transform duration-300"
                onClick={() => handleNavClick('/features')}
              >
                {t('features') || 'Features'}
              </Button>
              <Button
                variant="ghost"
                className="text-sm font-medium hover:scale-105 transition-transform duration-300"
                onClick={() => handleNavClick('/pricing')}
              >
                {t('pricing')}
              </Button>
            </nav>

            {/* 右侧工具栏 */}
            <div className="flex items-center space-x-3">
              <div className="hidden sm:flex items-center space-x-3">
                <LanguageSwitcher />
                <ThemeSwitcher />
              </div>

              {/* 移动端菜单按钮 */}
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden hover:scale-105 transition-transform duration-300"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>


              {/* 用户菜单 */}
              <div className="hidden lg:block">
                {loading ? (
                  <Button variant="ghost" disabled className="loading-pulse">
                    {t('loading')}
                  </Button>
                ) : user ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="text-sm hover:scale-105 transition-transform duration-300">
                        {t('profile')}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="theme-transition">
                      <DropdownMenuItem onClick={handleProfileClick}>
                        <UserCircle className="h-4 w-4 mr-2" />
                        {t('profilePage')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleSignOut}>
                        <LogOut className="h-4 w-4 mr-2" />
                        {t('logout')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Button
                    variant="default"
                    onClick={() => setAuthModalOpen(true)}
                    className="mystical-button-enhanced"
                  >
                    {t('login')}
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* 移动端菜单 */}
          {mobileMenuOpen && (
            <div className="lg:hidden border-t border-border/50 py-4 space-y-4 theme-transition">
              <div className="flex flex-col space-y-3">
                <Button
                  variant="ghost"
                  className="justify-start"
                  onClick={() => handleNavClick('/features')}
                >
                  {t('features') || 'Features'}
                </Button>
                <Button
                  variant="ghost"
                  className="justify-start"
                  onClick={() => handleNavClick('/pricing')}
                >
                  {t('pricing')}
                </Button>

                <div className="flex items-center space-x-3 px-4">
                  <LanguageSwitcher />
                  <ThemeSwitcher />
                </div>

                {loading ? (
                  <Button variant="ghost" disabled className="justify-start loading-pulse">
                    {t('loading')}
                  </Button>
                ) : user ? (
                  <div className="space-y-2">
                    <Button variant="ghost" className="justify-start" onClick={handleProfileClick}>
                      <UserCircle className="h-4 w-4 mr-2" />
                      {t('profilePage')}
                    </Button>
                    <Button variant="ghost" className="justify-start" onClick={handleSignOut}>
                      <LogOut className="h-4 w-4 mr-2" />
                      {t('logout')}
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="default"
                    className="mystical-button-enhanced mx-4"
                    onClick={() => setAuthModalOpen(true)}
                  >
                    {t('login')}
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </header>

      <AuthModal open={authModalOpen} onOpenChange={setAuthModalOpen} />
    </>
  );
};