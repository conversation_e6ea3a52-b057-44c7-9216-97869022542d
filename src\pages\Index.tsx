import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { DivinationInterface } from '@/components/DivinationInterface';
import { DivinationCard } from '@/components/DivinationCard';
import { HeroSection } from '@/components/HeroSection'; // 新增
import { FeaturesSection } from '@/components/FeaturesSection'; // 新增

import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { SavedRecordsPanel } from '@/components/SavedRecordsPanel';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Zap, Shield, Users, CheckCircle, XCircle, Sparkles, Heart, Scroll, BarChart3, Database, Cpu, Star, Sun, Flower } from 'lucide-react';
import { toast } from 'sonner';
import { useAuthStore } from '@/stores/authStore';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '@/lib/supabase';
import modernBg from '@/assets/modern-tech-bg.jpg';
import cuteBg from '@/assets/cute-warm-bg.jpg';
import classicBg from '@/assets/classic-chinese-bg.jpg';

const Index = () => {
  console.log('Index page rendering...')
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuthStore();
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();
  const [isLoaded, setIsLoaded] = useState(false);

  // 页面加载动画
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // 获取主题特色图标
  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      case 'classic': return Scroll;
      default: return Zap;
    }
  };

  const ThemeIcon = getThemeIcon();

  // 获取主题背景图
  const getThemeBackground = () => {
    switch (currentTheme) {
      case 'modern': return modernBg;
      case 'cute': return cuteBg;
      case 'classic': return classicBg;
      default: return modernBg;
    }
  };

  // 获取主题标题
  const getThemeTitle = () => {
    switch (currentTheme) {
      case 'modern': return t('modernTitle');
      case 'cute': return t('cuteTitle');
      case 'classic': return t('classicTitle');
      default: return t('classicTitle');
    }
  };

  // 获取主题描述
  const getThemeDescription = () => {
    switch (currentTheme) {
      case 'modern': return t('modernDesc');
      case 'cute': return t('cuteDesc');
      case 'classic': return t('classicDesc');
      default: return t('classicDesc');
    }
  };

  useEffect(() => {
    const handleAuthCallback = async () => {
      // 检查认证回调参数
      const authSuccess = searchParams.get('auth_success');
      const authError = searchParams.get('auth_error');

      if (authSuccess === 'true') {
        console.log('检测到认证成功回调');

        toast.success(t('loginSuccessWelcome'), {
          duration: 3000,
          icon: <CheckCircle className="h-4 w-4" />,
        });

        // 清理URL参数
        setSearchParams({}, { replace: true });
      } else if (authError) {
        console.log('检测到认证错误回调:', authError);
        toast.error(`${t('loginFailedIndex')}: ${decodeURIComponent(authError)}`, {
          duration: 5000,
          icon: <XCircle className="h-4 w-4" />,
        });

        // 清理URL参数
        setSearchParams({}, { replace: true });
      }
    };

    handleAuthCallback();
  }, [searchParams, setSearchParams]);

  const handleCTAClick = () => {
    document.getElementById('divination-interface')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  return (
    <div className={cn(
      "min-h-screen theme-transition",
      isLoaded ? "theme-changing" : "opacity-0"
    )}>
      <Header />

      {/* 使用新的Hero组件替换原有的三个主题Hero区域 */}
      <HeroSection onCTAClick={handleCTAClick} />

      {/* Main Interface */}
      <div id="divination-interface" className="py-16 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="max-w-4xl mx-auto">
            <DivinationInterface />
          </div>
        </div>
      </div>

      {/* 核心功能区域放在footer上面 */}
      <FeaturesSection />

      <Footer />
    </div>
  );
};

export default Index;
