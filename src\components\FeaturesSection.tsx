import React from 'react';
import { DivinationCard } from './DivinationCard';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { TrendingUp, Heart, Scroll, Shield, Users, Zap, Star, Sparkles, Flower } from 'lucide-react';
import { cn } from '@/lib/utils';

export const FeaturesSection: React.FC = () => {
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();

  const getFeatures = () => {
    const baseFeatures = [
      {
        id: 'planning',
        modern: {
          title: t('intelligentPlanning') || 'AI-Powered Analysis',
          icon: TrendingUp,
          description: t('modernAnalysisDesc') || 'Advanced algorithms analyze your divination with precision and insight.'
        },
        cute: {
          title: t('easyPlanning') || '✨ Magical Insights ✨',
          icon: Heart,
          description: t('cuteAnalysisDesc') || 'Discover your path with love, care, and magical wisdom!'
        },
        classic: {
          title: t('traditionalPlanning') || '传统智慧分析',
          icon: Scroll,
          description: t('classicAnalysisDesc') || '结合千年易经智慧，为您提供深度解读与指导。'
        }
      },
      {
        id: 'accuracy',
        modern: {
          title: t('highPrecisionResults') || 'High Precision Results',
          icon: Shield,
          description: t('modernPrecisionDesc') || 'State-of-the-art AI ensures accurate and reliable fortune readings.'
        },
        cute: {
          title: t('lovingGuidance') || '💖 Loving Guidance 💖',
          icon: Star,
          description: t('cutePrecisionDesc') || 'Gentle and caring advice to help you navigate life\'s journey.'
        },
        classic: {
          title: t('preciseReading') || '精准卦象解读',
          icon: Zap,
          description: t('classicPrecisionDesc') || '专业的卦象分析，准确把握时机与趋势。'
        }
      },
      {
        id: 'community',
        modern: {
          title: t('smartRecommendations') || 'Smart Recommendations',
          icon: Users,
          description: t('modernRecommendationDesc') || 'Personalized insights based on advanced pattern recognition.'
        },
        cute: {
          title: t('friendshipSupport') || '🌟 Friendship & Support 🌟',
          icon: Sparkles,
          description: t('cuteRecommendationDesc') || 'Join a community of like-minded souls on their spiritual journey.'
        },
        classic: {
          title: t('professionalConsultation') || '专业咨询服务',
          icon: Users,
          description: t('classicRecommendationDesc') || '资深易学专家提供个性化指导与建议。'
        }
      }
    ];

    return baseFeatures.map(feature => ({
      ...feature[currentTheme as keyof typeof feature],
      id: feature.id
    }));
  };

  const features = getFeatures();

  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        <div className="text-center mb-12 lg:mb-16 scroll-fade-in">
          <h2 className={cn(
            "text-3xl sm:text-4xl lg:text-5xl font-bold gradient-text mb-6",
            currentTheme === 'cute' && 'text-pink-600'
          )}>
            {t('coreFeaturesHighlight') || 'Core Features'}
          </h2>
          <p className={cn(
            "text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",
            currentTheme === 'cute' && 'text-pink-500'
          )}>
            {t('professionalAnalysisDesc') || 'Professional analysis powered by advanced AI technology'}
          </p>
        </div>
        
        {/* 特性卡片网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 auto-rows-fr">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            
            return (
              <DivinationCard 
                key={feature.id}
                title={feature.title}
                delay={index}
                className="h-full"
              >
                <div className="space-y-4 h-full flex flex-col">
                  <div className="text-center py-4">
                    <div className={cn(
                      "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center transition-transform duration-300 hover:scale-110",
                      currentTheme === 'cute' ? 
                        "bg-gradient-to-br from-primary/30 to-secondary/30" :
                        "bg-gradient-to-br from-primary to-accent"
                    )}>
                      <IconComponent className={cn(
                        "w-8 h-8",
                        currentTheme === 'cute' ? "text-primary" : "text-white"
                      )} />
                    </div>
                  </div>
                  
                  <div className="flex-1 flex items-center">
                    <p className={cn(
                      "text-sm text-center leading-relaxed",
                      currentTheme === 'cute' ? 
                        "text-pink-600" : 
                        "text-muted-foreground"
                    )}>
                      {feature.description}
                    </p>
                  </div>
                </div>
              </DivinationCard>
            );
          })}
        </div>
      </div>
    </section>
  );
};
