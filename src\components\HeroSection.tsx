import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { Zap, ChevronDown, Star, Heart, Scroll } from 'lucide-react';
import { cn } from '@/lib/utils';

// 背景图片资源
const backgroundImages = {
  modern: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&q=80',
  cute: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1920&q=80',
  classic: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&q=80'
};

interface HeroSectionProps {
  onCTAClick?: () => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ onCTAClick }) => {
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();
  const [scrollY, setScrollY] = useState(0);

  // 视差滚动效果
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const getThemeTitle = () => {
    switch (currentTheme) {
      case 'modern': return t('modernTitle') || 'AI-Powered Fortune Telling';
      case 'cute': return t('cuteTitle') || '✨ Magical Fortune Reading ✨';
      case 'classic': return t('classicTitle') || '易经六爻 · 智慧占卜';
      default: return t('appName');
    }
  };

  const getThemeDescription = () => {
    switch (currentTheme) {
      case 'modern': return t('modernDesc') || 'Advanced AI analysis meets ancient wisdom';
      case 'cute': return t('cuteDesc') || 'Discover your destiny with love and magic';
      case 'classic': return t('classicDesc') || '传承千年智慧，洞察人生玄机';
      default: return t('appDescription');
    }
  };

  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      case 'classic': return Scroll;
      default: return Zap;
    }
  };

  const ThemeIcon = getThemeIcon();

  const handleScrollToContent = () => {
    document.getElementById('divination-interface')?.scrollIntoView({ 
      behavior: 'smooth' 
    });
  };

  // Modern主题Hero
  if (currentTheme === 'modern') {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* 视差背景层 */}
        <div 
          className="absolute inset-0 bg-cover bg-center parallax-bg pattern-grid"
          style={{
            backgroundImage: `linear-gradient(rgba(0,0,0,0.7), rgba(15,23,42,0.6)), url(${backgroundImages.modern})`,
            transform: `translate3d(0, ${scrollY * 0.5}px, 0)`
          }}
        />
        
        {/* 内容层 */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8 sm:space-y-12">
            {/* 图标动画 */}
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center shadow-2xl hover:scale-110 transition-transform duration-300">
                <ThemeIcon className="w-10 h-10 text-white" />
              </div>
            </div>
            
            {/* 标题区域 */}
            <div className="space-y-6 scroll-fade-in">
              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold gradient-text leading-tight">
                {getThemeTitle()}
              </h1>
              <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {getThemeDescription()}
              </p>
            </div>
            
            {/* CTA按钮区域 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center scroll-fade-in-delay-1">
              <Button 
                variant="golden" 
                size="lg" 
                className="mystical-button-enhanced text-lg px-8 sm:px-12 py-4 sm:py-6 rounded-xl"
                onClick={onCTAClick || handleScrollToContent}
              >
                <Zap className="w-5 h-5 mr-2" />
                {t('startAiAnalysis')}
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 rounded-xl hover:bg-card/50 transition-all duration-300 hover:scale-105"
              >
                {t('learnMore')}
              </Button>
            </div>
          </div>
        </div>
        
        {/* 滚动指示器 */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce cursor-pointer" onClick={handleScrollToContent}>
          <ChevronDown className="w-6 h-6 text-muted-foreground hover:text-foreground transition-colors" />
        </div>
      </section>
    );
  }

  // Cute主题Hero
  if (currentTheme === 'cute') {
    return (
      <section 
        className="py-24 px-6 relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(255,182,193,0.9), rgba(255,218,185,0.8)), url(${backgroundImages.cute})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div className="max-w-4xl mx-auto text-center space-y-12">
          <div className="space-y-8 scroll-fade-in">
            <div className="flex justify-center space-x-4 mb-8">
              <Star className="w-8 h-8 text-yellow-400 animate-pulse" />
              <Heart className="w-10 h-10 text-pink-400 animate-bounce" />
              <Star className="w-8 h-8 text-yellow-400 animate-pulse" />
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold gradient-text leading-tight" style={{textShadow: '2px 2px 4px rgba(0,0,0,0.1)'}}>
              {getThemeTitle()}
            </h1>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border-4 border-pink-200">
              <p className="text-lg md:text-xl text-pink-700 leading-relaxed">
                {getThemeDescription()}
              </p>
            </div>
          </div>

          <div className="scroll-fade-in-delay-1">
            <Button 
              variant="golden" 
              size="lg" 
              className="mystical-button-enhanced text-lg px-12 py-6 rounded-3xl"
              onClick={onCTAClick || handleScrollToContent}
            >
              <Heart className="w-5 h-5 mr-2" />
              {t('startAiAnalysis')}
            </Button>
          </div>
        </div>
      </section>
    );
  }

  // Classic主题Hero
  return (
    <section 
      className="py-32 px-6 relative overflow-hidden"
      style={{
        backgroundImage: `linear-gradient(rgba(139,69,19,0.8), rgba(160,82,45,0.6)), url(${backgroundImages.classic})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <div className="max-w-5xl mx-auto text-center space-y-16">
        <div className="space-y-8 scroll-fade-in">
          <div className="flex justify-center mb-8">
            <div className="w-32 h-32 bg-gradient-to-br from-amber-600 to-orange-700 rounded-full flex items-center justify-center border-4 border-amber-400 shadow-2xl hover:scale-110 transition-transform duration-300">
              <Scroll className="w-16 h-16 text-white" />
            </div>
          </div>
          
          <h1 className="text-6xl md:text-7xl font-bold gradient-text leading-tight" style={{fontFamily: 'serif', textShadow: '3px 3px 6px rgba(0,0,0,0.3)'}}>
            {getThemeTitle()}
          </h1>
          
          <div className="bg-amber-50/90 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border-2 border-amber-300">
            <p className="text-xl md:text-2xl text-amber-900 leading-relaxed" style={{fontFamily: 'serif'}}>
              {getThemeDescription()}
            </p>
          </div>
        </div>

        <div className="scroll-fade-in-delay-1">
          <Button 
            variant="golden" 
            size="lg" 
            className="mystical-button-enhanced text-xl px-16 py-8 rounded-2xl"
            onClick={onCTAClick || handleScrollToContent}
          >
            <Scroll className="w-6 h-6 mr-3" />
            {t('startAiAnalysis')}
          </Button>
        </div>
      </div>
    </section>
  );
};
